import 'package:flutter/material.dart';
import '../../../core/models/device_type_model.dart';
import '../../../core/theme/app_theme.dart';

class DeviceTypeSelectionWidget extends StatefulWidget {
  final List<DeviceType> deviceTypes;
  final DeviceType? selectedDeviceType;
  final Function(DeviceType?) onDeviceTypeSelected;
  final bool isLoading;
  final String? errorMessage;

  const DeviceTypeSelectionWidget({
    super.key,
    required this.deviceTypes,
    required this.selectedDeviceType,
    required this.onDeviceTypeSelected,
    this.isLoading = false,
    this.errorMessage,
  });

  @override
  State<DeviceTypeSelectionWidget> createState() => _DeviceTypeSelectionWidgetState();
}

class _DeviceTypeSelectionWidgetState extends State<DeviceTypeSelectionWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '<PERSON>ọn loại thiết bị',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Lọc thiết bị theo loại để dễ dàng tìm kiếm',
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondary,
          ),
        ),
        const SizedBox(height: 16),
        
        if (widget.isLoading)
          _buildLoadingState()
        else if (widget.errorMessage != null)
          _buildErrorState()
        else
          _buildDeviceTypeDropdown(),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.errorRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.errorRed.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: AppTheme.errorRed),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.errorMessage!,
              style: TextStyle(color: AppTheme.errorRed),
            ),
          ),
        ],
      ),
    );
  }





  Widget _buildDeviceTypeDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Loại thiết bị',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<DeviceType?>(
          value: widget.selectedDeviceType,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.dividerGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.dividerGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.primaryBlue),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            hintText: 'Chọn loại thiết bị',
          ),
          items: _buildDropdownItems(),
          onChanged: (DeviceType? value) {
            widget.onDeviceTypeSelected(value);
          },
          icon: const Icon(Icons.arrow_drop_down),
        ),
      ],
    );
  }

  List<DropdownMenuItem<DeviceType?>> _buildDropdownItems() {
    List<DropdownMenuItem<DeviceType?>> items = [];

    // Add "Tất cả thiết bị" option - Simplified
    items.add(
      const DropdownMenuItem<DeviceType?>(
        value: null,
        child: Text('Tất cả thiết bị'),
      ),
    );

    // Add device type options - Simplified
    for (final deviceType in widget.deviceTypes) {
      items.add(
        DropdownMenuItem<DeviceType?>(
          value: deviceType,
          child: Text(deviceType.name),
        ),
      );
    }

    return items;
  }



}
