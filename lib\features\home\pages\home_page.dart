import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dashboard_page.dart';
import '../../user/pages/profile_page.dart';
import '../../automation/widgets/automation_main_page.dart';
import '../../home/<USER>/home_view_model.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _currentIndex = 0;
  int? _cachedHomeId; // Cache homeId để tránh rebuild

  @override
  Widget build(BuildContext context) {
    return Selector<HomeViewModel, ({int? currentHomeId, int homesCount})>(
      selector: (context, homeViewModel) {
        final currentHome = homeViewModel.currentHome;
        final homeId = currentHome?.homeId ??
                      (homeViewModel.homes.isNotEmpty ? homeViewModel.homes.first.homeId : 0);

        return (
          currentHomeId: homeId > 0 ? homeId : null,
          homesCount: homeViewModel.homes.length,
        );
      },
      builder: (context, data, child) {
        final homeId = data.currentHomeId ?? 0;

        if (_cachedHomeId != homeId && homeId > 0) {
          final oldHomeId = _cachedHomeId;
          _cachedHomeId = homeId;
          print('HomePage - homeId changed from $oldHomeId to: $homeId, homes count: ${data.homesCount}');
        }

        final List<Widget> pages = [
          const DashboardPage(),
          AutomationMainPage(homeId: homeId),
          const ProfilePage(),
        ];

        return Scaffold(
          body: IndexedStack(
            index: _currentIndex,
            children: pages,
          ),
          bottomNavigationBar: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.3),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: BottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              type: BottomNavigationBarType.fixed,
              backgroundColor: Colors.transparent,
              elevation: 0,
              selectedItemColor: Colors.blue[600],
              unselectedItemColor: Colors.grey[400],
              selectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 11,
              ),
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.home_outlined),
                  activeIcon: Icon(Icons.home),
                  label: 'Nhà',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.settings_input_component_outlined),
                  activeIcon: Icon(Icons.settings_input_component),
                  label: 'Automation',
                ),
             
                BottomNavigationBarItem(
                  icon: Icon(Icons.person_outline),
                  activeIcon: Icon(Icons.person),
                  label: 'Cá nhân',
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}




