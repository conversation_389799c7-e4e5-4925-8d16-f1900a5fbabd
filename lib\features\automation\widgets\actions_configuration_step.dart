import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/models/automation_models.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/device_type_model.dart';
import '../../../core/models/area_model.dart';
import '../../../core/repositories/home_repository.dart';
import '../../../core/repositories/device_repository.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'device_type_selection_widget.dart';

class ActionsConfigurationStep extends StatefulWidget {
  final int homeId;
  final List<AutomationAction> actions;
  final Function(List<AutomationAction>) onActionsChanged;

  const ActionsConfigurationStep({
    super.key,
    required this.homeId,
    required this.actions,
    required this.onActionsChanged,
  });

  @override
  State<ActionsConfigurationStep> createState() => _ActionsConfigurationStepState();
}

class _ActionsConfigurationStepState extends State<ActionsConfigurationStep> {
  late List<AutomationAction> _actions;
  List<Device> _availableDevices = [];
  List<Area> _availableAreas = [];
  List<DeviceType> _availableDeviceTypes = [];
  bool _isLoadingDevices = false;
  bool _isLoadingAreas = false;
  bool _isLoadingDeviceTypes = false;
  String? _selectedAreaFilter; // null = "Tất cả", "unassigned" = "Chưa gán khu vực", area_id = khu vực cụ thể
  DeviceType? _selectedDeviceTypeFilter; // null = "Tất cả thiết bị"

  @override
  void initState() {
    super.initState();
    _actions = List.from(widget.actions);

    // Load data after frame is built to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAvailableAreas();
      _loadAvailableDeviceTypes();
      _loadAvailableDevices();

      // Add initial action if empty
      if (_actions.isEmpty) {
        _addAction();
      }
    });
  }

  Future<void> _loadAvailableAreas() async {
    setState(() {
      _isLoadingAreas = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isNotEmpty) {
        final homeRepository = HomeRepository();
        final response = await homeRepository.getHomeAreas(token, widget.homeId);

        if (response.success && response.data != null) {
          setState(() {
            _availableAreas = response.data!;
          });
          print('[DEBUG] Loaded ${_availableAreas.length} areas for automation');
        } else {
          print('[DEBUG] Failed to load areas: ${response.message}');
        }
      }
    } catch (e) {
      print('Error loading areas: $e');
    } finally {
      setState(() {
        _isLoadingAreas = false;
      });
    }
  }

  Future<void> _loadAvailableDeviceTypes() async {
    setState(() {
      _isLoadingDeviceTypes = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isNotEmpty) {
        final deviceRepository = DeviceRepository();
        final response = await deviceRepository.getAllDeviceTypes(token);

        if (response.success && response.data != null) {
          setState(() {
            _availableDeviceTypes = response.data!;
          });
          print('[DEBUG] Loaded ${_availableDeviceTypes.length} device types for automation');
        }
      }
    } catch (e) {
      print('[DEBUG] Error loading device types: $e');
    } finally {
      setState(() {
        _isLoadingDeviceTypes = false;
      });
    }
  }

  Future<void> _loadAvailableDevices() async {
    setState(() {
      _isLoadingDevices = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isNotEmpty) {
        List<Device> devices = [];

        // Load devices based on device type filter first
        if (_selectedDeviceTypeFilter != null) {
          // Filter by device type
          final deviceRepository = DeviceRepository();
          final response = await deviceRepository.getDevicesByTypeInHome(
            token,
            widget.homeId,
            _selectedDeviceTypeFilter!.deviceTypeId!
          );
          if (response.success && response.data != null) {
            devices = response.data!;
            print('🔍 [DEBUG] Loaded ${devices.length} devices by type ${_selectedDeviceTypeFilter!.name}');
          }
        } else {
          // Load all devices in home
          final homeRepository = HomeRepository();
          final response = await homeRepository.getHomeDevices(token, widget.homeId);
          if (response.success && response.data != null) {
            devices = response.data!;
            print('[DEBUG] Loaded ${devices.length} devices (all) for automation');
          }
        }

        // Apply area filter if selected
        if (_selectedAreaFilter != null) {
          if (_selectedAreaFilter == 'unassigned') {
            devices = devices.where((device) => device.areaId == null).toList();
          } else {
            final areaId = int.tryParse(_selectedAreaFilter!);
            if (areaId != null) {
              devices = devices.where((device) => device.areaId == areaId).toList();
            }
          }
        }

        setState(() {
          _availableDevices = devices;
        });
      }
    } catch (e) {
      print('Error loading devices: $e');
    } finally {
      setState(() {
        _isLoadingDevices = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Cấu hình Hành động',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Thiết lập các hành động sẽ được thực hiện khi rule được kích hoạt',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 32),

          // Area Filter Dropdown
          _buildDropdownField(
            label: 'Lọc theo khu vực',
            value: _selectedAreaFilter ?? '',
            items: _getAreaFilterItems(),
            onChanged: (value) {
              setState(() {
                _selectedAreaFilter = value?.isEmpty == true ? null : value;
              });
              _loadAvailableDevices(); // Reload devices when area filter changes
            },
          ),
          const SizedBox(height: 24),

          // Device Type Selection
          DeviceTypeSelectionWidget(
            deviceTypes: _availableDeviceTypes,
            selectedDeviceType: _selectedDeviceTypeFilter,
            onDeviceTypeSelected: (deviceType) {
              setState(() {
                _selectedDeviceTypeFilter = deviceType;
              });
              _loadAvailableDevices(); // Reload devices when device type filter changes
            },
            isLoading: _isLoadingDeviceTypes,
          ),
          const SizedBox(height: 24),

          // Actions List
          ..._buildActionsList(),
          
          const SizedBox(height: 16),
          
          // Add Action Button
          _buildAddActionButton(),
          
          const SizedBox(height: 24),
          
          // Preview
          _buildPreview(),
        ],
      ),
    );
  }

  List<Widget> _buildActionsList() {
    return _actions.asMap().entries.map((entry) {
      final index = entry.key;
      final action = entry.value;
      
      return Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: _buildActionCard(action, index),
      );
    }).toList();
  }

  Widget _buildActionCard(AutomationAction action, int index) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryBlue,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Hành động',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ),
                if (_actions.length > 1)
                  IconButton(
                    onPressed: () => _removeAction(index),
                    icon: const Icon(Icons.delete),
                    color: AppTheme.errorRed,
                    iconSize: 20,
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Device Selection
            _buildDropdownField(
              label: 'Thiết bị',
              value: _getValidDeviceValue(action.deviceId),
              items: _getDeviceItems(),
              onChanged: (value) {
                if (value == null || value == 'loading' || value == 'empty') {
                  return; // Don't update for invalid values
                }

                final deviceId = int.tryParse(value) ?? 0;
                _updateAction(
                  index,
                  action.copyWith(
                    deviceId: deviceId,
                    deviceName: _getDeviceName(deviceId),
                  ),
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // Command Selection
            _buildDropdownField(
              label: 'Lệnh điều khiển',
              value: action.command,
              items: _getCommandItems(),
              onChanged: (value) => _updateAction(
                index,
                action.copyWith(command: value!),
              ),
            ),
            
           
            
            // Value and Delay
            
            
            const SizedBox(height: 12),
            
            // Action Preview
            Container(
              padding: const EdgeInsets.all(12),
             
              
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          items: items,
          onChanged: onChanged,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.dividerGrey),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required String label,
    required String value,
    required String hint,
    required Function(String) onChanged,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: TextEditingController(text: value),
          onChanged: onChanged,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.dividerGrey),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }

  Widget _buildAddActionButton() {
    return InkWell(
      onTap: _addAction,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppTheme.primaryBlue,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              color: AppTheme.primaryBlue,
            ),
            SizedBox(width: 8),
            Text(
              'Thêm hành động',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryBlue,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreview() {
    if (_actions.isEmpty) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.successGreen.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.successGreen.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.preview,
                color: AppTheme.successGreen,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Xem trước',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.successGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Sẽ thực hiện ${_actions.length} hành động:',
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          ..._actions.asMap().entries.map((entry) {
            final index = entry.key;
            final action = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '${index + 1}. ${_getActionPreview(action)}',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondary,
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  List<DropdownMenuItem<String>> _getAreaFilterItems() {
    List<DropdownMenuItem<String>> items = [
      const DropdownMenuItem(
        value: '',
        child: Text('Tất cả thiết bị'),
      ),
      const DropdownMenuItem(
        value: 'unassigned',
        child: Text('Thiết bị chưa gán khu vực'),
      ),
    ];

    // Add areas
    if (_availableAreas.isNotEmpty) {
      items.addAll(_availableAreas.map((area) {
        return DropdownMenuItem<String>(
          value: area.areaId.toString(),
          child: Text(area.displayName),
        );
      }));
    }

    return items;
  }

  List<DropdownMenuItem<String>> _getDeviceItems() {
    if (_isLoadingDevices) {
      return [
        const DropdownMenuItem(
          value: 'loading',
          child: Text('Đang tải thiết bị...'),
        ),
      ];
    }

    if (_availableDevices.isEmpty) {
      return [
        const DropdownMenuItem(
          value: 'empty',
          child: Text('Không có thiết bị nào'),
        ),
      ];
    }

    // Ensure unique device IDs and create items
    final uniqueDevices = <int, Device>{};
    for (var device in _availableDevices) {
      uniqueDevices[device.deviceId] = device;
    }

    return uniqueDevices.values.map((device) {
      return DropdownMenuItem<String>(
        value: device.deviceId.toString(),
        child: Text('${device.name} (ID: ${device.deviceId})'),
      );
    }).toList();
  }

  /// Đảm bảo deviceId value có trong danh sách items
  String _getValidDeviceValue(int deviceId) {
    // Nếu đang loading, return 'loading'
    if (_isLoadingDevices) {
      return 'loading';
    }

    // Nếu không có thiết bị, return 'empty'
    if (_availableDevices.isEmpty) {
      return 'empty';
    }

    // Nếu deviceId = 0 hoặc không hợp lệ, chọn thiết bị đầu tiên
    if (deviceId <= 0) {
      return _availableDevices.first.deviceId.toString();
    }

    // Kiểm tra xem deviceId có tồn tại trong danh sách không
    final deviceExists = _availableDevices.any((device) => device.deviceId == deviceId);

    if (deviceExists) {
      return deviceId.toString();
    } else {
      // Nếu không tồn tại, return deviceId của thiết bị đầu tiên
      return _availableDevices.first.deviceId.toString();
    }
  }

  List<DropdownMenuItem<String>> _getCommandItems() {
    // TODO: Load commands from device type API based on selected device
    return [
      const DropdownMenuItem(value: 'turn_on', child: Text('Bật')),
      const DropdownMenuItem(value: 'turn_off', child: Text('Tắt')),
    ];
  }

  String _getDeviceName(int deviceId) {
    try {
      final device = _availableDevices.firstWhere(
        (d) => d.deviceId == deviceId,
      );
      return '${device.name} (ID: ${device.deviceId})';
    } catch (e) {
      return 'Device $deviceId (Not found)';
    }
  }

  String _getActionPreview(AutomationAction action) {
    final deviceName = action.deviceName ?? _getDeviceName(action.deviceId);
    final commandText = _getCommandDisplayName(action.command);
    final valueText = action.value.isNotEmpty ? ' (${action.value})' : '';
    final delayText = action.delay != null && action.delay! > 0 ? ' sau ${action.delay}s' : '';
    
    return '$deviceName: $commandText$valueText$delayText';
  }

  String _getCommandDisplayName(String command) {
    switch (command) {
      case 'turn_on': return 'Bật';
      case 'turn_off': return 'Tắt';
      case 'set_brightness': return 'Đặt độ sáng';
      case 'set_temperature': return 'Đặt nhiệt độ';
      case 'set_speed': return 'Đặt tốc độ';
      default: return command;
    }
  }

  void _addAction() {
    // Use first available device or null
    final firstDevice = _availableDevices.isNotEmpty ? _availableDevices.first : null;

    setState(() {
      _actions.add(AutomationAction(
        deviceId: firstDevice?.deviceId ?? -1, // Use -1 instead of 0 to indicate "not selected"
        deviceName: firstDevice != null
            ? '${firstDevice.name} (ID: ${firstDevice.deviceId})'
            : 'Chưa chọn thiết bị',
        command: 'turn_on',
        value: '',
        delay: 0,
      ));
    });
    widget.onActionsChanged(_actions);
  }

  void _removeAction(int index) {
    setState(() {
      _actions.removeAt(index);
    });
    widget.onActionsChanged(_actions);
  }

  void _updateAction(int index, AutomationAction action) {
    setState(() {
      _actions[index] = action;
    });
    widget.onActionsChanged(_actions);
  }
}
