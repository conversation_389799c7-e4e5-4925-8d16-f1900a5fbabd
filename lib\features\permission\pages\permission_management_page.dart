import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/permission_view_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/permission_model.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/area_model.dart';

class PermissionManagementPage extends StatefulWidget {
  final Home home;

  const PermissionManagementPage({
    super.key,
    required this.home,
  });

  @override
  State<PermissionManagementPage> createState() => _PermissionManagementPageState();
}

class _PermissionManagementPageState extends State<PermissionManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_onTabChanged);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PermissionViewModel>().loadAllData(widget.home.homeId);
    });
  }

  void _onTabChanged() {
    if (!_tabController.indexIsChanging) {
      String filter = _tabController.index == 0 ? 'device' : 
                     _tabController.index == 1 ? 'area' : 'all';
      context.read<PermissionViewModel>().changeFilter(filter);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Quyền - ${widget.home.name}'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Thiết bị'),
            Tab(text: 'Khu vực'),
            Tab(text: 'Tất cả'),
          ],
        ),
      ),
      body: Consumer<PermissionViewModel>(
        builder: (context, viewModel, child) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
      if (viewModel.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(viewModel.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
        viewModel.clearError();
      }
    });
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }


          return TabBarView(
            controller: _tabController,
            children: [
              _buildPermissionsList(viewModel, 'device'),
              _buildPermissionsList(viewModel, 'area'),
              _buildPermissionsList(viewModel, 'all'),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddPermissionDialog(),
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildPermissionsList(PermissionViewModel viewModel, String type) {
    List<Permission> permissions;
    String emptyMessage;

    switch (type) {
      case 'device':
        permissions = viewModel.permissions.where((p) => p.deviceId != null).toList();
        emptyMessage = 'Chưa có quyền thiết bị';
        break;
      case 'area':
        permissions = viewModel.permissions.where((p) => p.areaId != null).toList();
        emptyMessage = 'Chưa có quyền khu vực';
        break;
      default:
        permissions = viewModel.permissions;
        emptyMessage = 'Chưa có quyền nào';
    }

    return RefreshIndicator(
      onRefresh: () => viewModel.loadPermissions(widget.home.homeId, filter: type),
      child: permissions.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.security, size: 48, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    emptyMessage,
                    style: const TextStyle(color: Colors.grey, fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _showAddPermissionDialog(),
                    child: const Text('Thêm quyền'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: permissions.length,
              itemBuilder: (context, index) {
                return _buildPermissionCard(permissions[index], viewModel);
              },
            ),
    );
  }

  Widget _buildPermissionCard(Permission permission, PermissionViewModel viewModel) {
    return Card(
  margin: const EdgeInsets.only(bottom: 8),
  child: Padding(
    padding: const EdgeInsets.all(12),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          permission.resourceType == 'device' ? Icons.devices : Icons.room,
          color: Colors.blue,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(_getResourceName(permission, viewModel),
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Text(_getUserName(permission, viewModel),
                  style: const TextStyle(fontSize: 12, color: Colors.grey)),
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                children: [
                  if (permission.canView)
                    _buildChip("Xem"),
                  if (permission.canControl)
                    _buildChip("Điều khiển"),
                  if (permission.canConfigure)
                    _buildChip("Cấu hình"),
                ],
              ),
            ],
          ),
        ),
        PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'edit') {
              _showEditPermissionDialog(permission);
            } else if (value == 'delete') {
              _deletePermission(permission, viewModel);
            }
          },
          itemBuilder: (context) => const [
            PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('Sửa'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Xóa', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    ),
  ),
);

}
Widget _buildChip(String label) {
  return Chip(
    label: Text(label),
    labelStyle: const TextStyle(fontSize: 10),
    visualDensity: VisualDensity.compact,
    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    padding: const EdgeInsets.symmetric(horizontal: 6),
  );
}


  String _getResourceName(Permission permission, PermissionViewModel viewModel) {
    if (permission.resourceType == 'device') {
      try {
        final device = viewModel.devices.firstWhere((d) => d.id == permission.resourceId);
        return device.name;
      } catch (e) {
        return 'Unknown Device';
      }
    } else {
      try {
        final area = viewModel.areas.firstWhere((a) => a.id == permission.resourceId);
        return area.name.toString();
      } catch (e) {
        return 'Unknown Area';
      }
    }
  }

  String _getUserName(Permission permission, PermissionViewModel viewModel) {
    try {
      final user = viewModel.users.firstWhere((u) => u.userId == permission.userId);
      return user.fullName ?? user.email;
    } catch (e) {
      return 'Unknown User';
    }
  }

  void _showAddPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => _AddPermissionDialog(
        home: widget.home,
        viewModel: context.read<PermissionViewModel>(),
      ),
    );
  }

  void _showEditPermissionDialog(Permission permission) {
    showDialog(
      context: context,
      builder: (context) => _EditPermissionDialog(
        home: widget.home,
        permission: permission,
        viewModel: context.read<PermissionViewModel>(),
      ),
    );
  }

  Future<void> _deletePermission(Permission permission, PermissionViewModel viewModel) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa quyền?'),
        content: const Text('Bạn có chắc muốn xóa quyền này không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await viewModel.removePermission(
        homeId: widget.home.id,
        permissionId: permission.id,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'Xóa thành công' : 'Xóa thất bại'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }
}

// Simplified Add Permission Dialog
class _AddPermissionDialog extends StatefulWidget {
  final Home home;
  final PermissionViewModel viewModel;

  const _AddPermissionDialog({
    required this.home,
    required this.viewModel,
  });

  @override
  State<_AddPermissionDialog> createState() => _AddPermissionDialogState();
}

class _AddPermissionDialogState extends State<_AddPermissionDialog> {
  String _selectedResourceType = 'device';
  HomeUser? _selectedUser;
  dynamic _selectedResource;
  bool _canView = true;
  bool _canControl = false;
  bool _canConfigure = false;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Thêm quyền'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Resource Type
            DropdownButtonFormField<String>(
              value: _selectedResourceType,
              decoration: const InputDecoration(
                labelText: 'Loại',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'device', child: Text('Thiết bị')),
                DropdownMenuItem(value: 'area', child: Text('Khu vực')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedResourceType = value!;
                  _selectedResource = null;
                });
              },
            ),
            const SizedBox(height: 16),

            // User Selection
            DropdownButtonFormField<HomeUser?>(
              value: _selectedUser,
              decoration: const InputDecoration(
                labelText: 'Người dùng',
                border: OutlineInputBorder(),
              ),
              items: widget.viewModel.users.map((user) {
                return DropdownMenuItem<HomeUser?>(
                  value: user,
                  child: Text(user.fullName ?? user.email),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedUser = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // Resource Selection
            if (_selectedResourceType == 'device')
              DropdownButtonFormField<Device?>(
                value: _selectedResource as Device?,
                decoration: const InputDecoration(
                  labelText: 'Thiết bị',
                  border: OutlineInputBorder(),
                ),
                items: widget.viewModel.devices.map((device) {
                  return DropdownMenuItem<Device?>(
                    value: device,
                    child: Text(device.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedResource = value;
                  });
                },
              )
            else
              DropdownButtonFormField<Area?>(
                value: _selectedResource as Area?,
                decoration: const InputDecoration(
                  labelText: 'Khu vực',
                  border: OutlineInputBorder(),
                ),
                items: widget.viewModel.areas.map((area) {
                  return DropdownMenuItem<Area?>(
                    value: area,
                    child: Text(area.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedResource = value;
                  });
                },
              ),
            const SizedBox(height: 16),

            // Permissions
            const Text('Quyền:', style: TextStyle(fontWeight: FontWeight.bold)),
            CheckboxListTile(
              title: const Text('Xem'),
              value: _canView,
              onChanged: (value) {
                setState(() {
                  _canView = value ?? false;
                  if (!_canView) {
                    _canControl = false;
                    _canConfigure = false;
                  }
                });
              },
            ),
            CheckboxListTile(
              title: const Text('Điều khiển'),
              value: _canControl,
              onChanged: _canView ? (value) {
                setState(() {
                  _canControl = value ?? false;
                  if (!_canControl) {
                    _canConfigure = false;
                  }
                });
              } : null,
            ),
            CheckboxListTile(
              title: const Text('Cấu hình'),
              value: _canConfigure,
              onChanged: _canControl ? (value) {
                setState(() {
                  _canConfigure = value ?? false;
                });
              } : null,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Hủy'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _addPermission,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Thêm'),
        ),
      ],
    );
  }

  Future<void> _addPermission() async {
    if (_selectedUser == null || _selectedResource == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn đầy đủ thông tin'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final request = PermissionRequest(
      userId: _selectedUser!.userId,
      deviceId: _selectedResourceType == 'device' ? _selectedResource!.id : null,
      areaId: _selectedResourceType == 'area' ? _selectedResource!.id : null,
      canView: _canView,
      canControl: _canControl,
      canConfigure: _canConfigure,
    );

    bool success;
    if (_selectedResourceType == 'device') {
      success = await widget.viewModel.addDevicePermission(
        homeId: widget.home.homeId,
        request: request,
      );
    } else {
      success = await widget.viewModel.addAreaPermission(
        homeId: widget.home.homeId,
        request: request,
      );
    }

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'Thêm quyền thành công' : 'Thêm quyền thất bại'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }
}

// Simplified Edit Permission Dialog
class _EditPermissionDialog extends StatefulWidget {
  final Home home;
  final Permission permission;
  final PermissionViewModel viewModel;

  const _EditPermissionDialog({
    required this.home,
    required this.permission,
    required this.viewModel,
  });

  @override
  State<_EditPermissionDialog> createState() => _EditPermissionDialogState();
}

class _EditPermissionDialogState extends State<_EditPermissionDialog> {
  late bool _canView;
  late bool _canControl;
  late bool _canConfigure;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _canView = widget.permission.canView;
    _canControl = widget.permission.canControl;
    _canConfigure = widget.permission.canConfigure;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Sửa quyền'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Resource info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Tài nguyên: ${_getResourceName()}'),
                Text('Người dùng: ${_getUserName()}'),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Permissions
          const Text('Quyền:', style: TextStyle(fontWeight: FontWeight.bold)),
          CheckboxListTile(
            title: const Text('Xem'),
            value: _canView,
            onChanged: (value) {
              setState(() {
                _canView = value ?? false;
                if (!_canView) {
                  _canControl = false;
                  _canConfigure = false;
                }
              });
            },
          ),
          CheckboxListTile(
            title: const Text('Điều khiển'),
            value: _canControl,
            onChanged: _canView ? (value) {
              setState(() {
                _canControl = value ?? false;
                if (!_canControl) {
                  _canConfigure = false;
                }
              });
            } : null,
          ),
          CheckboxListTile(
            title: const Text('Cấu hình'),
            value: _canConfigure,
            onChanged: _canControl ? (value) {
              setState(() {
                _canConfigure = value ?? false;
              });
            } : null,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Hủy'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updatePermission,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Cập nhật'),
        ),
      ],
    );
  }

  String _getResourceName() {
    if (widget.permission.resourceType == 'device') {
      try {
        final device = widget.viewModel.devices.firstWhere(
          (d) => d.id == widget.permission.resourceId,
        );
        return device.name;
      } catch (e) {
        return 'Unknown Device';
      }
    } else {
      try {
        final area = widget.viewModel.areas.firstWhere(
          (a) => a.id == widget.permission.resourceId,
        );
        return area.displayName;
      } catch (e) {
        return 'Unknown Area';
      }
    }
  }

  String _getUserName() {
    try {
      final user = widget.viewModel.users.firstWhere(
        (u) => u.userId == widget.permission.userId,
      );
      return user.fullName ?? user.email;
    } catch (e) {
      return 'Unknown User';
    }
  }

  Future<void> _updatePermission() async {
    setState(() {
      _isLoading = true;
    });

    final request = PermissionRequest(
      userId: widget.permission.userId,
      deviceId: widget.permission.deviceId,
      areaId: widget.permission.areaId,
      canView: _canView,
      canControl: _canControl,
      canConfigure: _canConfigure,
    );

    final success = await widget.viewModel.updatePermission(
      homeId: widget.home.homeId,
      permissionId: widget.permission.permissionId,
      request: request,
    );

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'Cập nhật thành công' : 'Cập nhật thất bại'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }
}