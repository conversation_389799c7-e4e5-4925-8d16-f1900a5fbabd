import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/device_view_model.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/home_model.dart';

class DeviceDetailPage extends StatefulWidget {
  final Device device;
  final Home home;

  const DeviceDetailPage({
    super.key,
    required this.device,
    required this.home,
  });

  @override
  State<DeviceDetailPage> createState() => _DeviceDetailPageState();
}

class _DeviceDetailPageState extends State<DeviceDetailPage> {
  // Lưu reference để tránh lỗi context
  late ScaffoldMessengerState _scaffoldMessenger;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Lưu reference an toàn
    _scaffoldMessenger = ScaffoldMessenger.of(context);
  }

  // Helper method để lấy device hiện tại (updated hoặc original)
  Device _getCurrentDevice(DeviceViewModel deviceViewModel) {
    return deviceViewModel.selectedDevice ?? widget.device;
  }

  @override
  void initState() {
    super.initState();
    // Don't load status automatically to avoid setState during build
  }

  Future<void> _loadDeviceStatus() async {
    if (!mounted) return;

    final deviceViewModel = context.read<DeviceViewModel>();
    final device = _getCurrentDevice(deviceViewModel);
    await deviceViewModel.getDeviceStatus(
      homeId: widget.home.id,
      deviceId: device.deviceId,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceViewModel>(
      builder: (context, deviceViewModel, child) {
        // Use updated device from ViewModel if available, otherwise use original
        final currentDevice = deviceViewModel.selectedDevice ?? widget.device;

        return Scaffold(
          appBar: AppBar(
            title: Text(currentDevice.name),
            backgroundColor: _getDeviceColor(currentDevice.type ?? 'unknown'),
            foregroundColor: Colors.white,
            elevation: 0,
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () async {
                  await _loadDeviceStatus();
                  if (mounted) {
                    _showSnackBar(
                      'Đã cập nhật trạng thái thiết bị',
                      Colors.green,
                    );
                  }
                },
                tooltip: 'Làm mới',
              ),
            ],
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Device Info Card
                _buildDeviceInfoCard(currentDevice),

                const SizedBox(height: 20),

                // Control Panel
                _buildControlPanel(currentDevice),

                const SizedBox(height: 20),

                // Device Status
                _buildDeviceStatus(currentDevice),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDeviceInfoCard(Device device) {
  return Container(
    width: double.infinity,
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          _getDeviceColor(device.type ?? 'unknown'),
          _getDeviceColor(device.type ?? 'unknown').withOpacity(0.8),
        ],
      ),
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: _getDeviceColor(device.type ?? 'unknown').withOpacity(0.3),
          spreadRadius: 2,
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Device Icon
            _buildDeviceIcon(device),
            const SizedBox(width: 16),

            // Device Info
            Expanded(
              child: _buildDeviceInfo(device),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Status Badge
        Align(
          alignment: Alignment.centerRight,
          child: _buildStatusBadge(device),
        ),

        // Additional info if needed

      ],
    ),
  );
}

Widget _buildDeviceIcon(Device device) {
  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.2),
      borderRadius: BorderRadius.circular(12),
    ),
    child: Icon(
      _getDeviceIcon(device.type ?? 'unknown'),
      color: Colors.white,
      size: 32,
    ),
  );
}

Widget _buildDeviceInfo(Device device) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        device.name,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      const SizedBox(height: 4),
      Text(
        device.type ?? 'Unknown Type',
        style: TextStyle(
          color: Colors.white.withOpacity(0.9),
          fontSize: 16,
        ),
      ),
    ],
  );
}

Widget _buildStatusBadge(Device device) {
  final isOnline = device.isOnline;
  final statusColor = isOnline ? Colors.green : Colors.red;
  final statusText = isOnline ? 'Đang kết nối' : 'Ngắt kết nối';

  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    decoration: BoxDecoration(
      color: statusColor.withOpacity(0.2),
      borderRadius: BorderRadius.circular(20),
      border: Border.all(
        color: statusColor.withOpacity(0.5),
        width: 1,
      ),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: statusColor,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 6),
        Text(
          statusText,
          style: TextStyle(
            color: statusColor.shade700,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    ),
  );
}


  Widget _buildControlPanel(Device device) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Điều khiển thiết bị',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildControlButton(
                    'Bật',
                    Icons.power_settings_new,
                    Colors.green,
                    () => _controlDevice('turn_on'),
                    isEnabled: device.isOnline && device.status != 'on',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildControlButton(
                    'Tắt',
                    Icons.power_off,
                    Colors.red,
                    () => _controlDevice('turn_off'),
                    isEnabled: device.isOnline && device.status != 'off',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton(String label, IconData icon, Color color, VoidCallback onPressed, {bool isEnabled = true}) {
    return ElevatedButton.icon(
      onPressed: isEnabled ? onPressed : null,
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: isEnabled ? color : Colors.grey,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }



  Widget _buildDeviceStatus(Device device) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Thông tin thiết bị',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatusRow('ID thiết bị', device.deviceId.toString()),
            if (device.uniqueIdentifier.isNotEmpty)
              _buildStatusRow('Unique ID', device.uniqueIdentifier, isMonospace: true),
            _buildStatusRow('Loại thiết bị', device.type ?? 'Unknown'),
            _buildStatusRow('Trạng thái', device.status ?? 'Unknown'),
            _buildStatusRow('Kết nối', device.isOnline ? 'Online' : 'Offline'),

            // Network Information - chỉ hiển thị IP Address
            if (device.networkInfo?.ipAddress != null) ...[
              _buildStatusRow('IP Address', device.networkInfo!.ipAddress!, isMonospace: true),
            ] else if (device.ipAddress != null && device.ipAddress!.isNotEmpty) ...[
              _buildStatusRow('IP Address', device.ipAddress!, isMonospace: true),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, {bool isMonospace = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          Flexible(
            child: Container(
              padding: isMonospace ? const EdgeInsets.symmetric(horizontal: 8, vertical: 2) : null,
              decoration: isMonospace ? BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ) : null,
              child: Text(
                value,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  fontFamily: isMonospace ? 'monospace' : null,
                  color: isMonospace ? Colors.blue[700] : null,
                ),
                textAlign: isMonospace ? TextAlign.right : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _controlDevice(String command) async {
    final deviceViewModel = context.read<DeviceViewModel>();
    final device = _getCurrentDevice(deviceViewModel);

    if (!device.isOnline) {
      _showSnackBar(
        'Thiết bị đang offline',
        Colors.red,
      );
      return;
    }

    final success = await deviceViewModel.controlDevice(
      homeId: widget.home.id,
      deviceId: device.deviceId,
      command: command,
      value: command == 'turn_on' ? 'on' : 'off',
    );

    if (mounted) {
      if (success) {
        // Tự động refresh device status sau khi điều khiển thành công
        await _loadDeviceStatus();

        _showSnackBar(
          'Điều khiển thiết bị thành công: $command',
          Colors.green,
        );
      } else if (deviceViewModel.errorMessage != null) {
        _showSnackBar(
          deviceViewModel.errorMessage!,
          Colors.red,
        );
      }
    }
  }



  IconData _getDeviceIcon(String type) {
    switch (type.toLowerCase()) {
      case 'smart light':
      case 'light':
        return Icons.lightbulb;
      case 'light sensor':
      case 'sensor':
        return Icons.sensors;
      case 'camera':
        return Icons.camera_alt;
      case 'temperature sensor':
        return Icons.thermostat;
      case 'smart ac':
      case 'ac':
      case 'air_conditioner':
        return Icons.ac_unit;
      case 'smart switch':
      case 'switch':
        return Icons.toggle_on;
      case 'fan':
        return Icons.air;
      default:
        return Icons.device_unknown;
    }
  }

  Color _getDeviceColor(String type) {
    switch (type.toLowerCase()) {
      case 'smart light':
      case 'light':
        return Colors.amber;
      case 'light sensor':
      case 'sensor':
        return Colors.green;
      case 'camera':
        return Colors.purple;
      case 'temperature sensor':
        return Colors.orange;
      case 'smart ac':
      case 'ac':
      case 'air_conditioner':
        return Colors.blue;
      case 'smart switch':
      case 'switch':
        return Colors.teal;
      case 'fan':
        return Colors.cyan;
      default:
        return Colors.grey;
    }
  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      _scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }


}
