import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/app_theme.dart';
import 'condition_type_selection_widget.dart';

class ThresholdConfigurationWidget extends StatefulWidget {
  final ConditionType conditionType;
  final String operator;
  final String value;
  final Function(String) onOperatorChanged;
  final Function(String) onValueChanged;

  const ThresholdConfigurationWidget({
    super.key,
    required this.conditionType,
    required this.operator,
    required this.value,
    required this.onOperatorChanged,
    required this.onValueChanged,
  });

  @override
  State<ThresholdConfigurationWidget> createState() => _ThresholdConfigurationWidgetState();
}

class _ThresholdConfigurationWidgetState extends State<ThresholdConfigurationWidget> {
  late TextEditingController _valueController;
  late TextEditingController _startTimeController;
  late TextEditingController _endTimeController;

  @override
  void initState() {
    super.initState();
    _valueController = TextEditingController(text: widget.value);
    _startTimeController = TextEditingController();
    _endTimeController = TextEditingController();
    
    // Parse time range if exists
    if (widget.conditionType == ConditionType.time && widget.value.contains('-')) {
      final parts = widget.value.split('-');
      if (parts.length == 2) {
        _startTimeController.text = parts[0].trim();
        _endTimeController.text = parts[1].trim();
      }
    }
  }

  @override
  void dispose() {
    _valueController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Đặt ngưỡng điều kiện',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Thiết lập ngưỡng cho ${widget.conditionType.displayName}',
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondary,
          ),
        ),
        const SizedBox(height: 16),
        
        _buildThresholdConfiguration(),
      ],
    );
  }

  Widget _buildThresholdConfiguration() {
    switch (widget.conditionType) {
      case ConditionType.temperature:
        return _buildNumericThreshold(
          title: 'Nhiệt độ',
          unit: '°C',
          min: -50,
          max: 100,
          examples: ['25°C', '30°C', '18°C'],
        );
      case ConditionType.humidity:
        return _buildNumericThreshold(
          title: 'Độ ẩm',
          unit: '%',
          min: 0,
          max: 100,
          examples: ['60%', '80%', '40%'],
        );
      case ConditionType.light:
        return _buildNumericThreshold(
          title: 'Cường độ ánh sáng',
          unit: 'Lux',
          min: 0,
          max: 100000,
          examples: ['100 Lux', '500 Lux', '1000 Lux'],
        );
      case ConditionType.motion:
        return _buildMotionThreshold();
      case ConditionType.time:
        return _buildTimeThreshold();
      case ConditionType.device:
        return _buildDeviceThreshold();
    }
  }

  Widget _buildNumericThreshold({
    required String title,
    required String unit,
    required double min,
    required double max,
    required List<String> examples,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Operator Selection
        _buildOperatorSelection(),
        const SizedBox(height: 16),
        
        // Value Input
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _valueController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                decoration: InputDecoration(
                  labelText: 'Giá trị $title',
                  suffixText: unit,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: AppTheme.primaryBlue),
                  ),
                ),
                onChanged: widget.onValueChanged,
              ),
            ),
            const SizedBox(width: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.primaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                unit,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryBlue,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Examples
        _buildExamples(examples),
        
        // Range indicator
        Text(
          'Phạm vi: ${min.toInt()} - ${max.toInt()} $unit',
          style: const TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildMotionThreshold() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Operator Selection (= hoặc !=)
        _buildOperatorSelection(),
        const SizedBox(height: 16),
        
        // Motion State Selection
        const Text(
          'Trạng thái chuyển động',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildMotionOption('detected', 'Có chuyển động', Icons.directions_run),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMotionOption('not_detected', 'Không có chuyển động', Icons.accessibility),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMotionOption(String value, String label, IconData icon) {
    final isSelected = _valueController.text == value;
    
    return InkWell(
      onTap: () {
        _valueController.text = value;
        widget.onValueChanged(value);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.warningOrange.withOpacity(0.1) : Colors.transparent,
          border: Border.all(
            color: isSelected ? AppTheme.warningOrange : AppTheme.dividerGrey,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? AppTheme.warningOrange : AppTheme.textSecondary,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isSelected ? AppTheme.warningOrange : AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeThreshold() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Operator Selection
        _buildOperatorSelection(),
        const SizedBox(height: 16),
        
        if (widget.operator == 'between') ...[
          // Time Range
          const Text(
            'Khoảng thời gian',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildTimeField(
                  controller: _startTimeController,
                  label: 'Từ',
                  onChanged: _updateTimeRange,
                ),
              ),
              const SizedBox(width: 12),
              const Text('đến'),
              const SizedBox(width: 12),
              Expanded(
                child: _buildTimeField(
                  controller: _endTimeController,
                  label: 'Đến',
                  onChanged: _updateTimeRange,
                ),
              ),
            ],
          ),
        ] else ...[
          // Single Time
          _buildTimeField(
            controller: _valueController,
            label: widget.operator == 'before' ? 'Trước' : 'Sau',
            onChanged: widget.onValueChanged,
          ),
        ],
        
        const SizedBox(height: 12),
        _buildExamples(['18:00-22:00', '06:00', '23:30']),
      ],
    );
  }

  Widget _buildTimeField({
    required TextEditingController controller,
    required String label,
    required Function(String) onChanged,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: 'HH:MM',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.primaryBlue),
        ),
        suffixIcon: IconButton(
          icon: const Icon(Icons.access_time),
          onPressed: () => _selectTime(controller, onChanged),
        ),
      ),
      onChanged: onChanged,
    );
  }

  Widget _buildDeviceThreshold() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Operator Selection
        _buildOperatorSelection(),
        const SizedBox(height: 16),
        
        // Device State Selection
        const Text(
          'Trạng thái thiết bị',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildDeviceStateOption('on', 'Bật', Icons.power_settings_new, AppTheme.successGreen),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDeviceStateOption('off', 'Tắt', Icons.power_off, AppTheme.errorRed),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDeviceStateOption(String value, String label, IconData icon, Color color) {
    final isSelected = _valueController.text == value;
    
    return InkWell(
      onTap: () {
        _valueController.text = value;
        widget.onValueChanged(value);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
          border: Border.all(
            color: isSelected ? color : AppTheme.dividerGrey,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : AppTheme.textSecondary,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isSelected ? color : AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperatorSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Toán tử so sánh',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.conditionType.operators.map((op) {
            final isSelected = widget.operator == op;
            return InkWell(
              onTap: () => widget.onOperatorChanged(op),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryBlue : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? AppTheme.primaryBlue : AppTheme.dividerGrey,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _getOperatorDisplayName(op),
                  style: TextStyle(
                    color: isSelected ? Colors.white : AppTheme.textPrimary,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildExamples(List<String> examples) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ví dụ:',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppTheme.textSecondary,
          ),
        ),
        const SizedBox(height: 4),
        Wrap(
          spacing: 8,
          children: examples.map((example) {
            return InkWell(
              onTap: () {
                final value = example.replaceAll(RegExp(r'[^\d\.\:\-]'), '');
                _valueController.text = value;
                widget.onValueChanged(value);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundGrey,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  example,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  String _getOperatorDisplayName(String operator) {
    switch (operator) {
      case '>': return 'Lớn hơn';
      case '<': return 'Nhỏ hơn';
      case '>=': return 'Lớn hơn hoặc bằng';
      case '<=': return 'Nhỏ hơn hoặc bằng';
      case '=': return 'Bằng';
      case '!=': return 'Khác';
      case 'between': return 'Trong khoảng';
      case 'before': return 'Trước';
      case 'after': return 'Sau';
      default: return operator;
    }
  }

  void _updateTimeRange(String value) {
    final timeRange = '${_startTimeController.text}-${_endTimeController.text}';
    widget.onValueChanged(timeRange);
  }

  Future<void> _selectTime(TextEditingController controller, Function(String) onChanged) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    
    if (picked != null) {
      final timeString = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      controller.text = timeString;
      onChanged(timeString);
    }
  }
}
