/// API Configuration cho Smart Home App
class ApiConfig {
  // Base URL cho development - đồng bộ với backend Go
  static const String baseUrl = 'http://10.115.73.114:8080';
  
  
  // API Endpoints
  static const String authEndpoint = '/api/auth';
  static const String passwordEndpoint = '/api/password';
  static const String userEndpoint = '/api/user';
  static const String adminHomesEndpoint = '/api/admin/homes';
  static const String devicesEndpoint = '/api/devices';
  static const String discoveryEndpoint = '/api/discovery';
  static const String permissionsEndpoint = '/api/permissions';
  static const String automationEndpoint = '/api/automation';
  
  // Request timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  /// Get authorization header
  static Map<String, String> getAuthHeaders(String token) {
    return {
      ...defaultHeaders,
      'Authorization': 'Bearer $token',
    };
  }
  
  // API Response codes
  static const int successCode = 200;
  static const int createdCode = 201;
  static const int unauthorizedCode = 401;
  static const int forbiddenCode = 403;
  static const int notFoundCode = 404;
  static const int serverErrorCode = 500;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Cache settings
  static const Duration cacheExpiry = Duration(minutes: 5);
  

  
  // Debug settings
  static const bool enableLogging = true;
  static const bool enableNetworkLogging = true;
}



/// API Response status
enum ApiStatus {
  success,
  error,
  loading,
  timeout,
  networkError,
}

/// Common API error messages
class ApiErrorMessages {
  static const String networkError = 'Lỗi kết nối mạng';
  static const String timeoutError = 'Hết thời gian chờ';
  static const String serverError = 'Lỗi máy chủ';
  static const String unauthorizedError = 'Không có quyền truy cập';
  static const String forbiddenError = 'Bị cấm truy cập';
  static const String notFoundError = 'Không tìm thấy dữ liệu';
  static const String validationError = 'Dữ liệu không hợp lệ';
  static const String unknownError = 'Lỗi không xác định';
}

/// API endpoint builder helper
class ApiEndpoints {
  // Auth endpoints
  static String login() => '${ApiConfig.authEndpoint}/login';
  static String register() => '${ApiConfig.authEndpoint}/register';
  static String verifyEmail() => '${ApiConfig.authEndpoint}/verify-email';
  static String resendVerification() => '${ApiConfig.authEndpoint}/resend-verification';
  static String verifyOtp() => '${ApiConfig.authEndpoint}/verify-otp';

  // Password endpoints
  static String forgotPassword() => '${ApiConfig.passwordEndpoint}/forgot/send-otp';
  static String resetPassword() => '${ApiConfig.passwordEndpoint}/forgot/reset';
  
  // User endpoints
  static String userProfile() => '${ApiConfig.userEndpoint}/profile';
  static String updateProfile() => '${ApiConfig.userEndpoint}/profile'; // PUT method
  static String changePassword() => '${ApiConfig.passwordEndpoint}/change-password'; // PUT method
  
  // Home endpoints
  static String homes() => ApiConfig.adminHomesEndpoint;
  static String home(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId';
  static String homeDetail(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/detail';
  static String homeAreas(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/areas';
  static String homeArea(int homeId, int areaId) => '${ApiConfig.adminHomesEndpoint}/$homeId/area/$areaId';
  static String homeUsers(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/users';
  static String homeDevices(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/devices';
  static String homeUsersWithRoles(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/users';
  static String promoteUser(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/users/promote';
  static String transferOwnership(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/transfer-ownership';
  static String removeUserFromHome(int homeId, int userId) => '${ApiConfig.adminHomesEndpoint}/$homeId/users/$userId';
  static String addDeviceToHome(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/devices';
  static String removeDeviceFromHome(int homeId, int deviceId) => '${ApiConfig.adminHomesEndpoint}/$homeId/devices/$deviceId';

  // Area management endpoints
  static String createArea(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/area';
  static String updateArea(int homeId, int areaId) => '${ApiConfig.adminHomesEndpoint}/$homeId/area/$areaId';
  static String deleteArea(int homeId, int areaId) => '${ApiConfig.adminHomesEndpoint}/$homeId/area/$areaId';

  // Device-Area management endpoints
  static String addDeviceToArea(int homeId, int areaId, int deviceId) => '${ApiConfig.adminHomesEndpoint}/$homeId/areas/$areaId/devices/$deviceId';
  static String removeDeviceFromArea(int homeId, int areaId, int deviceId) => '${ApiConfig.adminHomesEndpoint}/$homeId/areas/$areaId/devices/$deviceId';
  static String getDevicesInArea(int homeId, int areaId) => '${ApiConfig.adminHomesEndpoint}/$homeId/areas/$areaId/devices';

  // Invitation endpoints
  static String sendInvitation(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/invitations';
  static String getHomeInvitations(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/invitations';
  static String acceptInvitation(String token) => '/api/invitations/accept/$token';
  static String acceptInvitationPage(String token) => '/api/invitations/page/$token';
  
  // Global device endpoints (system-wide)
  static String devices() => ApiConfig.devicesEndpoint;
  static String device(int deviceId) => '${ApiConfig.devicesEndpoint}/$deviceId';
  static String availableDevices() => '${ApiConfig.devicesEndpoint}/available';

  // Home-specific device endpoints (theo cấu trúc backend)
  static String homeDevicesInHome(int homeId) => '/api/homes/$homeId/devices';
  static String deviceControl(int homeId, int deviceId) => '/api/homes/$homeId/devices/$deviceId/control';
  static String batchDeviceControl(int homeId) => '/api/homes/$homeId/devices/batch-control';
  static String deviceStatus(int homeId, int deviceId) => '/api/homes/$homeId/devices/$deviceId/status';
  static String allDeviceStatus(int homeId) => '/api/homes/$homeId/devices/status';
  static String deviceTypes(int homeId) => '/api/homes/$homeId/devices/types';
  static String devicesByType(int homeId, int deviceTypeId) => '/api/homes/$homeId/devices/by-type/$deviceTypeId';
  static String deviceProperties(int homeId, int deviceId) => '/api/homes/$homeId/devices/$deviceId/properties';
  static String deviceCommands(int homeId, int deviceId) => '/api/homes/$homeId/devices/$deviceId/commands';
  static String deviceHistory(int homeId, int deviceId) => '/api/homes/$homeId/devices/$deviceId/history';
  static String updateDeviceProperty(int homeId, int deviceId, String property) => '/api/homes/$homeId/devices/$deviceId/property/$property';
  static String startDeviceMonitoring(int homeId, int deviceId) => '/api/homes/$homeId/devices/$deviceId/monitor';
  
  // Discovery endpoints (updated structure)
  static String discoverDevices() => '${ApiConfig.discoveryEndpoint}/scan';
  static String deviceTemplates() => '${ApiConfig.discoveryEndpoint}/templates';
  static String connectionTypes() => '${ApiConfig.discoveryEndpoint}/connection-types';

  // Device Types Management endpoints (Read-only)
  static String deviceTypesManagement() => '/api/device-types';
  static String deviceTypeManagement(int deviceTypeId) => '/api/device-types/$deviceTypeId';
  
  // Permission endpoints (theo cấu trúc backend mới)
  static String homePermissions(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/permissions';
  static String homeDevicePermissions(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/permissions/devices';
  static String homeAreaPermissions(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/permissions/areas';
  static String addAreaPermission(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/permissions/area';
  static String addDevicePermission(int homeId) => '${ApiConfig.adminHomesEndpoint}/$homeId/permissions/device';
  static String updatePermission(int homeId, int permissionId) => '${ApiConfig.adminHomesEndpoint}/$homeId/permissions/$permissionId';
  static String deletePermission(int homeId, int permissionId) => '${ApiConfig.adminHomesEndpoint}/$homeId/permissions/$permissionId';

  // Member endpoints
  static String memberDevices() => '/api/member/devices';
  static String memberDevice(int deviceId) => '/api/member/devices/$deviceId';
  


  // Global Automation endpoints
  static String automationScheduleRule() => '/api/automation/rules/schedule';
  static String automationConditionRule() => '/api/automation/rules/condition';
  static String automationRules() => '/api/automation/rules';
  static String automationRule(int ruleId) => '/api/automation/rules/$ruleId';
  static String automationRuleStatus(int ruleId) => '/api/automation/rules/$ruleId/status';
  static String automationRuleExecute(int ruleId) => '/api/automation/rules/$ruleId/execute';
  static String automationRuleHistory(int ruleId) => '/api/automation/rules/$ruleId/history';
  static String automationTemplates() => '/api/automation/templates';
  static String createAutomationTemplate() => '/api/automation/templates';

  // Home-specific Automation endpoints
  static String homeAutomationRules(int homeId) => '/api/homes/$homeId/automation/rules';
  static String createHomeAutomationRule(int homeId) => '/api/homes/$homeId/automation/rules';
  static String homeAutomationRule(int homeId, int ruleId) => '/api/homes/$homeId/automation/rules/$ruleId';
  static String updateHomeAutomationRule(int homeId, int ruleId) => '/api/homes/$homeId/automation/rules/$ruleId';
  static String deleteHomeAutomationRule(int homeId, int ruleId) => '/api/homes/$homeId/automation/rules/$ruleId';
  static String toggleHomeAutomationRuleStatus(int homeId, int ruleId) => '/api/homes/$homeId/automation/rules/$ruleId/status';
  static String executeHomeAutomationRule(int homeId, int ruleId) => '/api/homes/$homeId/automation/rules/$ruleId/execute';
  static String homeAutomationExecutions(int homeId) => '/api/homes/$homeId/automation/executions';

  // Monitoring endpoints
  static String monitoringAlertRules() => '/api/monitoring/alert-rules';
  static String monitoringAlertRule(int ruleId) => '/api/monitoring/alert-rules/$ruleId';
  static String monitoringDeviceLogs(int deviceId) => '/api/monitoring/devices/$deviceId/logs';
  static String monitoringDeviceLogsByRange(int deviceId) => '/api/monitoring/devices/$deviceId/logs/range';
  static String monitoringUserDeviceLogs() => '/api/monitoring/user/device-logs';
  static String monitoringStatus() => '/api/monitoring/status';
  static String monitoringTriggerUpdate() => '/api/monitoring/trigger-update';

  // Home-specific Monitoring endpoints
  static String homeMonitoringAlertRules(int homeId) => '/api/homes/$homeId/monitoring/alert-rules';
  static String createHomeAlertRule(int homeId) => '/api/homes/$homeId/monitoring/alert-rules';
  static String homeAlertRule(int homeId, int ruleId) => '/api/homes/$homeId/monitoring/alert-rules/$ruleId';
  static String homeAlerts(int homeId) => '/api/homes/$homeId/monitoring/alerts';
  static String homeAlert(int homeId, int alertId) => '/api/homes/$homeId/monitoring/alerts/$alertId';
  static String homeMonitoringDeviceLogs(int homeId) => '/api/homes/$homeId/monitoring/device-logs';
  static String homeMonitoringEvents(int homeId) => '/api/homes/$homeId/monitoring/events';
  static String homeMonitoringStatistics(int homeId) => '/api/homes/$homeId/monitoring/statistics';
  static String homeMonitoringReports(int homeId) => '/api/homes/$homeId/monitoring/reports';

  

  
}
