import 'package:fe_flutter/features/auth/pages/login_page.dart';
import 'package:fe_flutter/features/home/<USER>/home_page.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'features/auth/view_models/auth_view_model.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _hasCheckedAuth = false;

  @override
  void initState() {
    super.initState();
    // Check auth status when app starts - only once
    if (!_hasCheckedAuth) {
      _hasCheckedAuth = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          context.read<AuthViewModel>().checkAuthStatus();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, authViewModel, child) {
        print('🔍 [DEBUG] AuthWrapper rebuild - isLoading: ${authViewModel.isLoading}, isAuthenticated: ${authViewModel.isAuthenticated}');

        if (authViewModel.isLoading) {
          print('🔍 [DEBUG] AuthWrapper showing loading');
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else if (authViewModel.isAuthenticated) {
          print('🔍 [DEBUG] AuthWrapper showing HomePage');
          return const HomePage();
        } else {
          print('🔍 [DEBUG] AuthWrapper showing LoginPage');
          return const LoginPage();
        }
      },
    );
  }
}
