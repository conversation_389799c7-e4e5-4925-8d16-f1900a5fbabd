import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';
import '../models/monitoring_models.dart';

class MonitoringService {
  static final MonitoringService _instance = MonitoringService._internal();
  factory MonitoringService() => _instance;
  MonitoringService._internal();

  Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  Map<String, String> _getHeaders(String? token) {
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // ==================== ALERT RULES ====================

  /// Get all alert rules for user
  Future<List<AlertRule>> getAlertRules() async {
    try {
      final token = await _getAuthToken();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/monitoring/alert-rules'),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> rulesJson = data['data'] ?? [];
        return rulesJson.map((json) => AlertRule.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load alert rules: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading alert rules: $e');
    }
  }

  /// Get alert rules for specific home
  Future<List<AlertRule>> getHomeAlertRules(int homeId) async {
    try {
      final token = await _getAuthToken();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/homes/$homeId/monitoring/alert-rules'),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> rulesJson = data['data'] ?? [];
        return rulesJson.map((json) => AlertRule.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load home alert rules: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading home alert rules: $e');
    }
  }

  /// Create alert rule for home
  Future<AlertRule> createHomeAlertRule(int homeId, AlertRule rule) async {
    try {
      final token = await _getAuthToken();
      final requestBody = rule.toJson();
      requestBody['home_id'] = homeId;

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/homes/$homeId/monitoring/alert-rules'),
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return AlertRule.fromJson(data['data']);
      } else {
        throw Exception('Failed to create alert rule: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error creating alert rule: $e');
    }
  }

  /// Update alert rule
  Future<AlertRule> updateAlertRule(int ruleId, AlertRule rule) async {
    try {
      final token = await _getAuthToken();
      final response = await http.put(
        Uri.parse('${ApiConfig.baseUrl}/api/monitoring/alert-rules/$ruleId'),
        headers: _getHeaders(token),
        body: json.encode(rule.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AlertRule.fromJson(data['data']);
      } else {
        throw Exception('Failed to update alert rule: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating alert rule: $e');
    }
  }

  /// Delete alert rule
  Future<void> deleteAlertRule(int ruleId) async {
    try {
      final token = await _getAuthToken();
      final response = await http.delete(
        Uri.parse('${ApiConfig.baseUrl}/api/monitoring/alert-rules/$ruleId'),
        headers: _getHeaders(token),
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to delete alert rule: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error deleting alert rule: $e');
    }
  }

  // ==================== ALERTS ====================

  /// Get alerts for home
  Future<List<MonitoringAlert>> getHomeAlerts(int homeId) async {
    try {
      final token = await _getAuthToken();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/homes/$homeId/monitoring/alerts'),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> alertsJson = data['data'] ?? [];
        return alertsJson.map((json) => MonitoringAlert.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load home alerts: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading home alerts: $e');
    }
  }

  /// Update alert (mark as read, resolved, etc.)
  Future<MonitoringAlert> updateHomeAlert(int homeId, int alertId, Map<String, dynamic> updates) async {
    try {
      final token = await _getAuthToken();
      final response = await http.put(
        Uri.parse('${ApiConfig.baseUrl}/api/homes/$homeId/monitoring/alerts/$alertId'),
        headers: _getHeaders(token),
        body: json.encode(updates),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return MonitoringAlert.fromJson(data['data']);
      } else {
        throw Exception('Failed to update alert: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating alert: $e');
    }
  }

  // ==================== DEVICE LOGS ====================

  /// Get device logs for specific device
  Future<List<DeviceActivityLog>> getDeviceLogs(int deviceId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      final token = await _getAuthToken();
      String url = '${ApiConfig.baseUrl}/api/monitoring/devices/$deviceId/logs';
      
      if (startDate != null && endDate != null) {
        url = '${ApiConfig.baseUrl}/api/monitoring/devices/$deviceId/logs/range';
        url += '?start_date=${startDate.toIso8601String()}&end_date=${endDate.toIso8601String()}';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> logsJson = data['data'] ?? [];
        return logsJson.map((json) => DeviceActivityLog.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load device logs: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading device logs: $e');
    }
  }

  /// Get all device logs for user
  Future<List<DeviceActivityLog>> getUserDeviceLogs() async {
    try {
      final token = await _getAuthToken();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/monitoring/user/device-logs'),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> logsJson = data['data'] ?? [];
        return logsJson.map((json) => DeviceActivityLog.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load user device logs: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading user device logs: $e');
    }
  }

  /// Get device logs for home
  Future<List<DeviceActivityLog>> getHomeDeviceLogs(int homeId) async {
    try {
      final token = await _getAuthToken();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/homes/$homeId/monitoring/device-logs'),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> logsJson = data['data'] ?? [];
        return logsJson.map((json) => DeviceActivityLog.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load home device logs: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading home device logs: $e');
    }
  }

  // ==================== MONITORING STATUS & STATISTICS ====================

  /// Get monitoring status
  Future<Map<String, dynamic>> getMonitoringStatus() async {
    try {
      final token = await _getAuthToken();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/monitoring/status'),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data'] ?? {};
      } else {
        throw Exception('Failed to load monitoring status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading monitoring status: $e');
    }
  }

  /// Get home monitoring statistics
  Future<MonitoringStats> getHomeMonitoringStatistics(int homeId) async {
    try {
      final token = await _getAuthToken();
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/homes/$homeId/monitoring/statistics'),
        headers: _getHeaders(token),
        body: json.encode({}), // Empty body for POST request
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return MonitoringStats.fromJson(data['data']);
      } else {
        throw Exception('Failed to load monitoring statistics: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading monitoring statistics: $e');
    }
  }

  /// Get home monitoring events
  Future<List<MonitoringEvent>> getHomeMonitoringEvents(int homeId) async {
    try {
      final token = await _getAuthToken();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/homes/$homeId/monitoring/events'),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> eventsJson = data['data'] ?? [];
        return eventsJson.map((json) => MonitoringEvent.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load monitoring events: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading monitoring events: $e');
    }
  }

  /// Generate home monitoring report
  Future<Map<String, dynamic>> generateHomeMonitoringReport(int homeId, {
    DateTime? startDate,
    DateTime? endDate,
    String? reportType,
  }) async {
    try {
      final token = await _getAuthToken();
      final requestBody = <String, dynamic>{};
      
      if (startDate != null) requestBody['start_date'] = startDate.toIso8601String();
      if (endDate != null) requestBody['end_date'] = endDate.toIso8601String();
      if (reportType != null) requestBody['report_type'] = reportType;

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/homes/$homeId/monitoring/reports'),
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['data'] ?? {};
      } else {
        throw Exception('Failed to generate monitoring report: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error generating monitoring report: $e');
    }
  }

  /// Trigger device update
  Future<void> triggerDeviceUpdate() async {
    try {
      final token = await _getAuthToken();
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/monitoring/trigger-update'),
        headers: _getHeaders(token),
        body: json.encode({}),
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to trigger device update: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error triggering device update: $e');
    }
  }
}
