import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/home_view_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/area_model.dart';
import '../../../core/models/device_model.dart';
import '../../device/pages/device_detail_page.dart';



class AreaDevicesPage extends StatefulWidget {
  final Home home;
  final Area area;

  const AreaDevicesPage({
    super.key,
    required this.home,
    required this.area,
  });

  @override
  State<AreaDevicesPage> createState() => _AreaDevicesPageState();
}

class _AreaDevicesPageState extends State<AreaDevicesPage> {
  // Lưu reference để tránh lỗi context
  late ScaffoldMessengerState _scaffoldMessenger;
  late NavigatorState _navigator;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Lưu reference an toàn
    _scaffoldMessenger = ScaffoldMessenger.of(context);
    _navigator = Navigator.of(context);
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<HomeViewModel>();
      viewModel.loadAreaDevices(widget.home.id, widget.area.id);
      viewModel.loadHomeDevices(widget.home.id); // Load all devices for adding
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.area.name} - Thiết bị'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddDeviceDialog,
          ),
        ],
      ),
      body: Consumer<HomeViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (viewModel.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    viewModel.errorMessage!,
                    style: TextStyle(color: Colors.red[600]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => viewModel.loadAreaDevices(widget.home.id, widget.area.id),
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => viewModel.loadAreaDevices(widget.home.homeId, widget.area.areaId),
            child: viewModel.areaDevices.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.devices_outlined, size: 64, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'Chưa có thiết bị nào trong khu vực này',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _showAddDeviceDialog,
                          icon: const Icon(Icons.add),
                          label: const Text('Thêm thiết bị'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[600],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: viewModel.areaDevices.length,
                    itemBuilder: (context, index) {
                      final device = viewModel.areaDevices[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: _getDeviceColor(device.type.toString().toString()).withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      _getDeviceIcon(device.type.toString()),
                                      color: _getDeviceColor(device.type.toString()),
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          device.name,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          device.type.toString(),
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  
                                ],
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                 
                                 ElevatedButton.icon(
                                      onPressed: () => _navigateToDeviceControl(device),
                                      label: Text(device.isControllable ? 'Điều khiển' : 'Xem dữ liệu'),
                                      icon: Icon(device.isControllable ? Icons.settings_remote : Icons.sensors),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: _getDeviceColor(device.type ?? 'unknown'),
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  const SizedBox(width: 15),
                                   ElevatedButton.icon(
                                    onPressed: () => _removeDeviceFromArea(device),
                                    label: const Text('Xóa khỏi khu vực'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red[600],
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          );
        },
      ),
    );
  }

  IconData _getDeviceIcon(String type) {
    switch (type.toLowerCase()) {
      case 'smart light':
      case 'light':
      case 'đèn':
        return Icons.lightbulb_outline;
      case 'light sensor':
      case 'sensor':
      case 'cảm biến':
        return Icons.sensors;
      case 'camera':
        return Icons.camera_alt_outlined;
      case 'temperature sensor':
        return Icons.thermostat;
      case 'smart ac':
      case 'ac':
      case 'điều hòa':
        return Icons.ac_unit;
      case 'smart switch':
      case 'switch':
      case 'công tắc':
        return Icons.toggle_on_outlined;
      case 'fan':
      case 'quạt':
        return Icons.air;
      default:
        return Icons.device_unknown;
    }
  }

  Color _getDeviceColor(String type) {
    switch (type.toLowerCase()) {
      case 'smart light':
      case 'light':
      case 'đèn':
        return Colors.amber;
      case 'light sensor':
      case 'sensor':
      case 'cảm biến':
        return Colors.green;
      case 'camera':
        return Colors.purple;
      case 'temperature sensor':
        return Colors.orange;
      case 'smart ac':
      case 'ac':
      case 'điều hòa':
        return Colors.blue;
      case 'smart switch':
      case 'switch':
      case 'công tắc':
        return Colors.teal;
      case 'fan':
      case 'quạt':
        return Colors.cyan;
      default:
        return Colors.grey;
    }
  }

  void _showAddDeviceDialog() {
    final viewModel = context.read<HomeViewModel>();
    
    // Get devices that are not in this area
    final availableDevices = viewModel.homeDevices.where((device) {
      return !viewModel.areaDevices.any((areaDevice) => areaDevice.id == device.id);
    }).toList();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Thêm thiết bị vào ${widget.area.name}'),
        content: SizedBox(
          width: double.maxFinite,
          child: availableDevices.isEmpty
              ? const Text('Không có thiết bị nào để thêm.')
              : ListView.builder(
                  shrinkWrap: true,
                  itemCount: availableDevices.length,
                  itemBuilder: (context, index) {
                    final device = availableDevices[index];
                    return ListTile(
                      leading: Icon(_getDeviceIcon(device.type.toString())),
                      title: Text(device.name),
                      subtitle: Text(device.type.toString()),
                      onTap: () async {
                        _navigator.pop();
                        final success = await viewModel.addDeviceToArea(
                          homeId: widget.home.id,
                          areaId: widget.area.id,
                          deviceId: device.id,
                        );

                        if (mounted) {
                          if (success) {
                            _showSnackBar(
                              'Đã thêm ${device.name} vào ${widget.area.name}',
                              Colors.green,
                            );
                          } else if (viewModel.errorMessage != null) {
                            _showSnackBar(
                              viewModel.errorMessage!,
                              Colors.red,
                            );
                          }
                        }
                      },
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => _navigator.pop(),
            child: const Text('Hủy'),
          ),
        ],
      ),
    );
  }

  void _showDeviceDetails(Device device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(device.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Loại thiết bị:', device.type.toString()),
            _buildDetailRow('Trạng thái:', device.status == 'online' ? 'Trực tuyến' : 'Ngoại tuyến'),
            _buildDetailRow('Khu vực:', widget.area.name.toString()),
            _buildDetailRow('ID:', device.id.toString()),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => _navigator.pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _navigateToDeviceControl(Device device) {
    if (!device.isControllable) {
      // Hiển thị thông báo cho thiết bị sensor
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.sensors, color: Colors.white),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Thiết bị "${device.name}" là cảm biến, không thể điều khiển',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DeviceDetailPage(
          device: device,
          home: widget.home,
        ),
      ),
    );
  }

  Future<void> _removeDeviceFromArea(Device device) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text('Bạn có chắc muốn xóa ${device.name} khỏi khu vực ${widget.area.name}?\n\nThiết bị sẽ được chuyển về danh sách chung.'),
        actions: [
          TextButton(
            onPressed: () => _navigator.pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => _navigator.pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final viewModel = context.read<HomeViewModel>();
      final success = await viewModel.removeDeviceFromArea(
        homeId: widget.home.homeId,
        areaId: widget.area.areaId,
        deviceId: device.id,
      );

      if (mounted) {
        if (success) {
          _showSnackBar(
            'Đã xóa ${device.name} khỏi ${widget.area.name}',
            Colors.green,
          );
        } else if (viewModel.errorMessage != null) {
          _showSnackBar(
            viewModel.errorMessage!,
            Colors.red,
          );
        }
      }
    }
  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      _scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
        ),
      );
    }
  }
}
