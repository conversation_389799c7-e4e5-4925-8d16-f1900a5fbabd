import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/models/automation_models.dart';

class ScheduleConfigurationStep extends StatefulWidget {
  final String ruleName;
  final String ruleDescription;
  final AutomationSchedule? schedule;
  final Function(String) onNameChanged;
  final Function(String) onDescriptionChanged;
  final Function(AutomationSchedule) onScheduleChanged;

  const ScheduleConfigurationStep({
    super.key,
    required this.ruleName,
    required this.ruleDescription,
    this.schedule,
    required this.onNameChanged,
    required this.onDescriptionChanged,
    required this.onScheduleChanged,
  });

  @override
  State<ScheduleConfigurationStep> createState() => _ScheduleConfigurationStepState();
}

class _ScheduleConfigurationStepState extends State<ScheduleConfigurationStep> {
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  
  TimeOfDay _selectedTime = const TimeOfDay(hour: 18, minute: 0);
  List<String> _selectedDays = [];
  String _timezone = 'Asia/Ho_Chi_Minh';

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.ruleName);
    _descriptionController = TextEditingController(text: widget.ruleDescription);
    
    if (widget.schedule != null) {
      final schedule = widget.schedule!;

      // Safely parse time with validation
      try {
        if (schedule.time.isNotEmpty && schedule.time.contains(':')) {
          final timeParts = schedule.time.split(':');
          if (timeParts.length >= 2) {
            final hour = int.tryParse(timeParts[0].trim());
            final minute = int.tryParse(timeParts[1].trim());

            if (hour != null && minute != null && hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
              _selectedTime = TimeOfDay(hour: hour, minute: minute);
            } else {
              print('⚠️ Invalid time values: hour=$hour, minute=$minute');
              _selectedTime = const TimeOfDay(hour: 9, minute: 0); // Default fallback
            }
          } else {
            print('⚠️ Invalid time format: ${schedule.time}');
            _selectedTime = const TimeOfDay(hour: 9, minute: 0); // Default fallback
          }
        } else {
          print('Empty or invalid time: "${schedule.time}"');
          _selectedTime = const TimeOfDay(hour: 9, minute: 0);
        }
      } catch (e) {
        print('Error parsing schedule time "${schedule.time}": $e');
        _selectedTime = const TimeOfDay(hour: 9, minute: 0);
      }

      _selectedDays = List.from(schedule.days);
      _timezone = schedule.timezone;
    } else {
      // Default to weekdays
      _selectedDays = [
        AutomationDays.monday,
        AutomationDays.tuesday,
        AutomationDays.wednesday,
        AutomationDays.thursday,
        AutomationDays.friday,
      ];
    }
    
    _updateSchedule();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _updateSchedule() {
    final timeString = '${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')}';
    
    final schedule = AutomationSchedule(
      time: timeString,
      days: _selectedDays,
      timezone: _timezone,
      repeat: 'daily',
    );
    
    widget.onScheduleChanged(schedule);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Cấu hình Lịch trình',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Thiết lập thời gian và ngày thực hiện automation rule',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 32),
          
          // Rule Name
          _buildTextField(
            controller: _nameController,
            label: 'Tên Rule',
            hint: 'Ví dụ: Bật đèn buổi tối',
            onChanged: widget.onNameChanged,
            required: true,
          ),
          
          const SizedBox(height: 20),
          
          // Rule Description
          _buildTextField(
            controller: _descriptionController,
            label: 'Mô tả (Tùy chọn)',
            hint: 'Mô tả chi tiết về automation rule này',
            onChanged: widget.onDescriptionChanged,
            maxLines: 3,
          ),
          
          const SizedBox(height: 32),
          
          // Time Selection
          _buildTimeSelection(),
          
          const SizedBox(height: 32),
          
          // Days Selection
          _buildDaysSelection(),
          
          const SizedBox(height: 32),
          
          // Timezone
          _buildTimezoneSelection(),
          
          const SizedBox(height: 32),
          
          // Preview
          _buildPreview(),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required Function(String) onChanged,
    bool required = false,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
            if (required)
              const Text(
                ' *',
                style: TextStyle(
                  color: AppTheme.errorRed,
                  fontSize: 16,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          onChanged: onChanged,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppTheme.dividerGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppTheme.primaryBlue),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Thời gian thực hiện',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        InkWell(
          onTap: _selectTime,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: AppTheme.dividerGrey),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.access_time,
                  color: AppTheme.primaryBlue,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  '${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const Spacer(),
                const Icon(
                  Icons.keyboard_arrow_down,
                  color: AppTheme.textSecondary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDaysSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ngày trong tuần',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        
        // Quick select buttons
        Row(
          children: [
            _buildQuickSelectButton(
              'Tất cả',
              () => _selectAllDays(),
            ),
            const SizedBox(width: 8),
            _buildQuickSelectButton(
              'Thứ 2-6',
              () => _selectWeekdays(),
            ),
            const SizedBox(width: 8),
            _buildQuickSelectButton(
              'Cuối tuần',
              () => _selectWeekends(),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Days grid
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: AutomationDays.all.map((day) {
            final isSelected = _selectedDays.contains(day);
            return InkWell(
              onTap: () => _toggleDay(day),
              child: Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryBlue : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? AppTheme.primaryBlue : AppTheme.dividerGrey,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    AutomationDays.getShortDisplayName(day),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppTheme.surfaceWhite : AppTheme.textPrimary,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickSelectButton(String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: AppTheme.primaryBlue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryBlue,
          ),
        ),
      ),
    );
  }

  Widget _buildTimezoneSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Múi giờ',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.dividerGrey),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.public,
                color: AppTheme.primaryBlue,
                size: 20,
              ),
              const SizedBox(width: 12),
              const Text(
                'Asia/Ho_Chi_Minh (GMT+7)',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPreview() {
    if (_selectedDays.isEmpty) return const SizedBox.shrink();
    
    final daysText = _selectedDays.length == 7 
        ? 'hàng ngày'
        : _selectedDays.map((day) => AutomationDays.getDisplayName(day)).join(', ');
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.infoBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.infoBlue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.preview,
                color: AppTheme.infoBlue,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Xem trước',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.infoBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Rule sẽ chạy lúc ${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')} $daysText',
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  void _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppTheme.primaryBlue,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
      _updateSchedule();
    }
  }

  void _toggleDay(String day) {
    setState(() {
      if (_selectedDays.contains(day)) {
        _selectedDays.remove(day);
      } else {
        _selectedDays.add(day);
      }
    });
    _updateSchedule();
  }

  void _selectAllDays() {
    setState(() {
      _selectedDays = List.from(AutomationDays.all);
    });
    _updateSchedule();
  }

  void _selectWeekdays() {
    setState(() {
      _selectedDays = [
        AutomationDays.monday,
        AutomationDays.tuesday,
        AutomationDays.wednesday,
        AutomationDays.thursday,
        AutomationDays.friday,
      ];
    });
    _updateSchedule();
  }

  void _selectWeekends() {
    setState(() {
      _selectedDays = [
        AutomationDays.saturday,
        AutomationDays.sunday,
      ];
    });
    _updateSchedule();
  }
}
