import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_theme.dart';
import '../view_models/automation_view_model.dart';
import '../pages/automation_rules_page.dart';
import '../pages/create_automation_rule_page.dart';

class AutomationMainPage extends StatefulWidget {
  final int homeId;

  const AutomationMainPage({
    super.key,
    required this.homeId,
  });

  @override
  State<AutomationMainPage> createState() => _AutomationMainPageState();
}

class _AutomationMainPageState extends State<AutomationMainPage> {
  AutomationViewModel? _viewModel;
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();

    // Initialize view model - chỉ một lần
    if (!_hasInitialized) {
      _hasInitialized = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _viewModel = Provider.of<AutomationViewModel>(context, listen: false);
          _viewModel!.addListener(_handleErrorChanges);

          if (widget.homeId > 0) {
            _viewModel!.setCurrentHomeId(widget.homeId);
          } else {
            // Auto-load homeId if not provided or invalid
            _viewModel!.autoLoadHomeId();
          }
        }
      });
    }
  }

  void _handleErrorChanges() {
    if (_viewModel?.errorMessage != null && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_viewModel!.errorMessage!),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Đóng',
                textColor: Colors.white,
                onPressed: () {
                  _viewModel!.clearError();
                },
              ),
            ),
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _viewModel?.removeListener(_handleErrorChanges);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundGrey,
      
      appBar: AppBar(
        leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
        title: const Text('Automation'),
        backgroundColor: AppTheme.primaryBlue,
        foregroundColor: AppTheme.surfaceWhite,
        elevation: 0,
      ),
      body: AutomationRulesPage(homeId: widget.homeId),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToCreateRule(context),
        backgroundColor: AppTheme.primaryBlue,
        child: const Icon(Icons.add, color: AppTheme.surfaceWhite),
      ),
    );
  }

  void _navigateToCreateRule(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateAutomationRulePage(homeId: widget.homeId),
      ),
    ).then((_) {
      // Refresh rules list after creating new rule
      if (mounted && _viewModel != null) {
        _viewModel!.loadAutomationRules();
      }
    });
  }
}
