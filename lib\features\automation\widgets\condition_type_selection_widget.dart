import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';

enum ConditionType {
  temperature,
  motion,
  time,
  device,
  humidity,
  light
}

class ConditionTypeSelectionWidget extends StatefulWidget {
  final ConditionType? selectedType;
  final Function(ConditionType?) onTypeSelected;
  final bool isLoading;
  final String? errorMessage;

  const ConditionTypeSelectionWidget({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
    this.isLoading = false,
    this.errorMessage,
  });

  @override
  State<ConditionTypeSelectionWidget> createState() => _ConditionTypeSelectionWidgetState();
}

class _ConditionTypeSelectionWidgetState extends State<ConditionTypeSelectionWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Chọn loại điều kiện',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.infoBlue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppTheme.infoBlue.withValues(alpha: 0.3)),
          ),
          child: const Row(
            children: [
              Icon(Icons.info_outline, color: AppTheme.infoBlue, size: 18),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Chọn điều kiện sẽ kích hoạt automation rule',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Các loại điều kiện có sẵn:',
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondary,
          ),
        ),
        const SizedBox(height: 16),
        
        if (widget.isLoading)
          _buildLoadingState()
        else if (widget.errorMessage != null)
          _buildErrorState()
        else
          _buildConditionTypeDropdown(),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.errorRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.errorRed.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: AppTheme.errorRed),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.errorMessage!,
              style: TextStyle(color: AppTheme.errorRed),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConditionTypeDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Loại điều kiện',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<ConditionType?>(
          value: widget.selectedType,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.dividerGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.dividerGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.primaryBlue),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            hintText: 'Chọn loại điều kiện',
          ),
          items: _buildDropdownItems(),
          onChanged: (ConditionType? value) {
            widget.onTypeSelected(value);
          },
          icon: const Icon(Icons.arrow_drop_down),
        ),
      ],
    );
  }

  List<DropdownMenuItem<ConditionType?>> _buildDropdownItems() {
    return ConditionType.values.map((conditionType) {
      final config = _getConditionTypeConfig(conditionType);
      return DropdownMenuItem<ConditionType?>(
        value: conditionType,
        child: Row(
          children: [
            Icon(
              config.icon,
              size: 20,
              color: config.color,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    config.title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  Text(
                    config.subtitle,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }).toList();
  }



  ConditionTypeConfig _getConditionTypeConfig(ConditionType type) {
    switch (type) {
      case ConditionType.temperature:
        return ConditionTypeConfig(
          title: 'Nhiệt độ',
          subtitle: 'VD: > 30°C',
          icon: Icons.thermostat,
          color: AppTheme.errorRed,
        );
      case ConditionType.motion:
        return ConditionTypeConfig(
          title: 'Chuyển động',
          subtitle: 'VD: Có người',
          icon: Icons.directions_run,
          color: AppTheme.warningOrange,
        );
      case ConditionType.time:
        return ConditionTypeConfig(
          title: 'Thời gian',
          subtitle: 'VD: Sau 18:00',
          icon: Icons.access_time,
          color: AppTheme.primaryBlue,
        );
      case ConditionType.device:
        return ConditionTypeConfig(
          title: 'Thiết bị',
          subtitle: 'VD: Đèn bật',
          icon: Icons.devices,
          color: AppTheme.secondaryPurple,
        );
      case ConditionType.humidity:
        return ConditionTypeConfig(
          title: 'Độ ẩm',
          subtitle: 'VD: > 70%',
          icon: Icons.water_drop,
          color: AppTheme.infoBlue,
        );
      case ConditionType.light:
        return ConditionTypeConfig(
          title: 'Ánh sáng',
          subtitle: 'VD: < 100 lux',
          icon: Icons.wb_sunny,
          color: AppTheme.successGreen,
        );
    }
  }
}

class ConditionTypeConfig {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;

  ConditionTypeConfig({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });
}

// Extension để lấy thông tin condition type
extension ConditionTypeExtension on ConditionType {
  String get displayName {
    switch (this) {
      case ConditionType.temperature:
        return 'Nhiệt độ';
      case ConditionType.motion:
        return 'Chuyển động';
      case ConditionType.time:
        return 'Thời gian';
      case ConditionType.device:
        return 'Thiết bị';
      case ConditionType.humidity:
        return 'Độ ẩm';
      case ConditionType.light:
        return 'Ánh sáng';
    }
  }

  String get unit {
    switch (this) {
      case ConditionType.temperature:
        return '°C';
      case ConditionType.humidity:
        return '%';
      case ConditionType.light:
        return 'Lux';
      case ConditionType.motion:
        return '';
      case ConditionType.time:
        return '';
      case ConditionType.device:
        return '';
    }
  }

  List<String> get operators {
    switch (this) {
      case ConditionType.temperature:
      case ConditionType.humidity:
      case ConditionType.light:
        return ['>', '<', '>=', '<=', '='];
      case ConditionType.motion:
      case ConditionType.device:
        return ['=', '!='];
      case ConditionType.time:
        return ['between', 'before', 'after'];
    }
  }
}
