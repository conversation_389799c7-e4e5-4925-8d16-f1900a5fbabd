import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../view_models/automation_view_model.dart';

class AutomationStatsCard extends StatelessWidget {
  final AutomationViewModel viewModel;

  const AutomationStatsCard({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Thống kê Automation',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            
            // Stats grid
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Tổng Rules',
                    viewModel.getRulesCountByType('all').toString(),
                    Icons.list,
                    AppTheme.primaryBlue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Hoạt động',
                    viewModel.getRulesCountByType('active').toString(),
                    Icons.play_circle,
                    AppTheme.successGreen,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Lịch trình',
                    viewModel.getRulesCountByType('schedule').toString(),
                    Icons.schedule,
                    AppTheme.infoBlue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Điều kiện',
                    viewModel.getRulesCountByType('condition').toString(),
                    Icons.sensors,
                    AppTheme.warningOrange,
                  ),
                ),
              ],
            ),
            

          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
