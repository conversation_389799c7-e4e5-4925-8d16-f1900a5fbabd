import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/user_model.dart';
import '../models/api_response.dart';
import '../config/api_config.dart';

class AuthRepository {
  // HTTP Status Codes
  static const int _statusOk = 200;
  static const int _statusCreated = 201;

  // Timeout duration
  static const Duration _timeoutDuration = Duration(seconds: 30);

  // Helper method để tạo headers
  Map<String, String> _getHeaders([String? token]) {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  // Helper method để xử lý response
  Future<ApiResponse<T>> _handleResponse<T>(
    Future<http.Response> request,
    T Function(Map<String, dynamic>)? dataParser,
    String errorMessage,
  ) async {
    try {
      print(' DEBUG - Sending HTTP request...');
      final response = await request.timeout(_timeoutDuration);
      print(' DEBUG - Received response: ${response.statusCode}');
      print(' DEBUG - Response body: ${response.body}');
      final data = jsonDecode(response.body) as Map<String, dynamic>;
      print(' DEBUG - Parsed data: $data');

      // Check for backend response format: {"status": true/false, "message": "...", "data": {...}}
      final status = data['status'] as bool? ?? data['success'] as bool? ?? false;

      if (response.statusCode == _statusOk || response.statusCode == _statusCreated) {
        if (status) {
          return ApiResponse<T>.success(
            data: dataParser != null ? dataParser(data) : null,
            message: data['message'] as String? ?? 'Success',
          );
        } else {
          return ApiResponse<T>.error(
            message: data['message'] as String? ?? errorMessage,
          );
        }
      } else {
        return ApiResponse<T>.error(
          message: data['message'] as String? ?? errorMessage,
        );
      }
    } on SocketException {
      return ApiResponse<T>.error(
        message: 'Không có kết nối internet',
      );
    } on HttpException {
      return ApiResponse<T>.error(
        message: 'Lỗi kết nối server',
      );
    } on FormatException catch (e) {
      return ApiResponse<T>.error(
        message: 'Lỗi định dạng dữ liệu: ${e.message}',
      );
    } catch (e) {
      return ApiResponse<T>.error(
        message: '$errorMessage: ${e.toString()}',
      );
    }
  }
  
  // User Registration
  Future<ApiResponse<Map<String, dynamic>>> register({
    required String email,
    required String password,
  }) async {
    return _handleResponse<Map<String, dynamic>>(
      http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiEndpoints.register()}'),
        headers: _getHeaders(),
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      ),
      (data) => data,
      'Đăng ký thất bại',
    );
  }

  // Email Verification
  Future<ApiResponse<void>> verifyEmail({
    required String email,
    required String otp,
  }) async {
    return _handleResponse<void>(
      http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiEndpoints.verifyEmail()}'),
        headers: _getHeaders(),
        body: jsonEncode({
          'email': email,
          'otp': otp,
        }),
      ),
      null,
      'Xác thực email thất bại',
    );
  }

  // Resend Email Verification
  Future<ApiResponse<void>> resendVerification(String email) async {
    return _handleResponse<void>(
      http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiEndpoints.resendVerification()}'),
        headers: _getHeaders(),
        body: jsonEncode({'email': email}),
      ),
      null,
      'Gửi lại mã xác thực thất bại',
    );
  }

  // Step 1: Login with email/password (returns OTP requirement)
  Future<ApiResponse<Map<String, dynamic>>> login(String email, String password) async {
    return _handleResponse<Map<String, dynamic>>(
      http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiEndpoints.login()}'),
        headers: _getHeaders(),
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      ),
      (data) => data,
      'Đăng nhập thất bại',
    );
  }

  // Step 2: Verify OTP and get access token
  Future<ApiResponse<Map<String, dynamic>>> verifyOTP(String email, String otp) async {
    return _handleResponse<Map<String, dynamic>>(
      http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiEndpoints.verifyOtp()}'),
        headers: _getHeaders(),
        body: jsonEncode({
          'email': email,
          'otp': otp,
        }),
      ),
      (data) => data,
      'Xác thực OTP thất bại',
    );
  }

  // Forgot Password
  Future<ApiResponse<void>> forgotPassword(String email) async {
    return _handleResponse<void>(
      http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiEndpoints.forgotPassword()}'),
        headers: _getHeaders(),
        body: jsonEncode({'email': email}),
      ),
      null,
      'Quên mật khẩu thất bại',
    );
  }

  // Resend OTP
  Future<ApiResponse<void>> resendOTP(String email) async {
    return _handleResponse<void>(
      http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/auth/resend-otp'),
        headers: _getHeaders(),
        body: jsonEncode({'email': email}),
      ),
      null,
      'Gửi lại OTP thất bại',
    );
  }

  // Reset Password
  Future<ApiResponse<void>> resetPassword({
    required String email,
    required String otp,
    required String newPassword,
  }) async {
    return _handleResponse<void>(
      http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiEndpoints.resetPassword()}'),
        headers: _getHeaders(),
        body: jsonEncode({
          'email': email,
          'otp': otp,
          'new_password': newPassword,
        }),
      ),
      null,
      'Đặt lại mật khẩu thất bại',
    );
  }

  // Change Password (for authenticated users)
  Future<ApiResponse<void>> changePassword({
    required String token,
    required String oldPassword,
    required String newPassword,
  }) async {
    final url = '${ApiConfig.baseUrl}${ApiEndpoints.changePassword()}';

    return _handleResponse<void>(
      http.put(
        Uri.parse(url),
        headers: _getHeaders(token),
        body: jsonEncode({
          'old_password': oldPassword,
          'new_password': newPassword,
        }),
      ),
      null,
      'Đổi mật khẩu thất bại',
    );
  }

  // Get User Profile
  Future<ApiResponse<User>> getProfile(String token) async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}${ApiEndpoints.userProfile()}'),
        headers: _getHeaders(token),
      ).timeout(_timeoutDuration);

      print('Profile Response Status: ${response.statusCode}');
      print('Profile Response Body: ${response.body}');

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode == _statusOk) {
        // Try different response structures
        Map<String, dynamic> userData;

        if (data['data'] != null) {
          // Structure: {"status": true, "data": {...}}
          userData = data['data'] as Map<String, dynamic>;
        } else if (data.containsKey('user_id') || data.containsKey('email')) {
          // Structure: {"user_id": 15, "email": "...", ...}
          userData = data;
        } else {
          throw Exception('Invalid response structure: ${data.keys}');
        }

        print('User Data: $userData');

        final user = User.fromJson(userData);
        return ApiResponse<User>.success(
          data: user,
          message: data['message'] as String? ?? 'Success',
        );
      } else {
        return ApiResponse<User>.error(
          message: data['message'] as String? ?? 'Lấy thông tin profile thất bại',
        );
      }
    } on SocketException {
      return ApiResponse<User>.error(
        message: 'Không có kết nối internet',
      );
    } on HttpException {
      return ApiResponse<User>.error(
        message: 'Lỗi kết nối server',
      );
    } on FormatException catch (e) {
      return ApiResponse<User>.error(
        message: 'Lỗi định dạng dữ liệu: ${e.message}',
      );
    } catch (e) {
      print('Profile Error: $e');
      return ApiResponse<User>.error(
        message: 'Lấy thông tin profile thất bại: ${e.toString()}',
      );
    }
  }

  // Update User Profile
  Future<ApiResponse<User>> updateProfile({
    required String token,
    String? fullName,
    String? phoneNumber,
    String? avatarUrl,
  }) async {
    // Chỉ gửi các field không null
    final Map<String, dynamic> updateData = {};

    if (fullName != null) updateData['full_name'] = fullName;
    if (phoneNumber != null) updateData['phone_number'] = phoneNumber;
    if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;

    final url = '${ApiConfig.baseUrl}${ApiEndpoints.updateProfile()}';
    print(' DEBUG - Update Profile URL: $url');
    print(' DEBUG - Update Profile Data: $updateData');
    print(' DEBUG - Headers: ${_getHeaders(token)}');

    return _handleResponse<User>(
      http.put(
        Uri.parse(url),
        headers: _getHeaders(token),
        body: jsonEncode(updateData),
      ),
      (data) {
        print(' DEBUG - Update Profile Response: $data');

        // Try different response structures
        Map<String, dynamic>? userData;

        if (data['data'] != null) {
          // Structure: {"status": true, "data": {...}}
          userData = data['data'] as Map<String, dynamic>;
          return User.fromJson(userData);
        } else if (data.containsKey('user_id') || data.containsKey('email')) {
          // Structure: {"user_id": 15, "email": "...", ...}
          userData = data;
          return User.fromJson(userData);
        } else if (data['status'] == true) {
          // Backend response: {"status": true, "message": "Cập nhật thông tin thành công"}
          // No user data returned, but update was successful
          print(' DEBUG - Update successful, no user data returned');
          throw Exception('UPDATE_SUCCESS_NO_DATA'); // Special exception to indicate success
        } else {
          // If no user data and no success status, something went wrong
          print(' DEBUG - No user data in response and no success status');
          throw Exception('Invalid response structure');
        }
      },
      'Cập nhật profile thất bại',
    );
  }

  // Legacy method for backward compatibility
  Future<User> getProfileLegacy(String token) async {
    final response = await getProfile(token);
    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw Exception(response.message);
    }
  }

  // Legacy method for verifyOTPAndGetToken
  Future<Map<String, dynamic>> verifyOTPAndGetToken(String email, String otp) async {
    final response = await verifyOTP(email, otp);
    if (response.success && response.data != null) {
      return response.data!;
    } else {
      throw Exception(response.message);
    }
  }

  


}