import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/models/automation_models.dart';

class AutomationRuleCard extends StatelessWidget {
  final AutomationRule rule;
  final VoidCallback? onTap;
  final Function(bool)? onToggle;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onExecute;

  const AutomationRuleCard({
    super.key,
    required this.rule,
    this.onTap,
    this.onToggle,
    this.onEdit,
    this.onDelete,
    this.onExecute,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with status and actions
              Row(
                children: [
                  // Status indicator
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: rule.isActive ? AppTheme.successGreen : AppTheme.errorRed,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  
                  // Rule name
                  Expanded(
                    child: Text(
                      rule.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  
                  // Action buttons
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Execute button
                    
                      
                      // Edit button
                      if (onEdit != null)
                        IconButton(
                          onPressed: onEdit,
                          icon: const Icon(Icons.edit),
                          iconSize: 20,
                          color: AppTheme.textSecondary,
                          tooltip: 'Chỉnh sửa',
                        ),
                      
                      // Delete button
                      if (onDelete != null)
                        IconButton(
                          onPressed: onDelete,
                          icon: const Icon(Icons.delete),
                          iconSize: 20,
                          color: AppTheme.errorRed,
                          tooltip: 'Xóa',
                        ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Rule type and quick info
              Row(
                children: [
                  _buildRuleTypeBadge(),
                  const SizedBox(width: 8),
                  
                   
                   _buildExecutionStatus(),
                
                  const SizedBox(width: 60),
                  // Toggle switch
                  if (onToggle != null)
                    Switch(
                      value: rule.isActive,
                      onChanged: onToggle,
                      activeColor: AppTheme.successGreen,
                      inactiveThumbColor: AppTheme.errorRed,
                    ),
                ],
              ),
              
             
              
         
             
              
           
              
              // Execution status
              Row(
                children: [
                
                  
                  
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRuleTypeBadge() {
    Color badgeColor;
    IconData badgeIcon;
    String badgeText;

    switch (rule.ruleType) {
      case AutomationRuleType.schedule:
        badgeColor = AppTheme.infoBlue;
        badgeIcon = Icons.schedule;
        badgeText = 'Lịch trình';
        break;
      case AutomationRuleType.condition:
        badgeColor = AppTheme.warningOrange;
        badgeIcon = Icons.sensors;
        badgeText = 'Điều kiện';
        break;
      default:
        badgeColor = AppTheme.textSecondary;
        badgeIcon = Icons.help;
        badgeText = 'Khác';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            badgeIcon,
            size: 12,
            color: badgeColor,
          ),
          const SizedBox(width: 4),
          Text(
            badgeText,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: badgeColor,
            ),
          ),
        ],
      ),
    );
  }

  String _getRuleQuickInfo() {
    if (rule.ruleType == AutomationRuleType.schedule && rule.schedule != null) {
      final schedule = rule.schedule!;
      final daysText = schedule.days.length == 7 
          ? 'Hàng ngày' 
          : schedule.days.map((day) => AutomationDays.getShortDisplayName(day)).join(', ');
      return 'Lúc ${schedule.time} • $daysText';
    } else if (rule.ruleType == AutomationRuleType.condition && rule.conditions != null) {
      final conditionsCount = rule.conditions!.length;
      return '$conditionsCount điều kiện${rule.conditionLogic != null ? ' (${rule.conditionLogic})' : ''}';
    }
    return '';
  }

  String _getActionsPreview() {
    if (rule.actions.isEmpty) return 'Không có hành động';
    
    if (rule.actions.length == 1) {
      final action = rule.actions.first;
      return '${action.deviceName ?? 'Device ${action.deviceId}'} → ${action.command}';
    } else {
      return '${rule.actions.length} hành động';
    }
  }

  Widget _buildExecutionStatus() {
    if (rule.lastExecuted != null) {
      final lastExecuted = rule.lastExecuted!;
      final now = DateTime.now();
      final difference = now.difference(lastExecuted);
      
      String timeAgo;
      if (difference.inMinutes < 1) {
        timeAgo = 'Vừa xong';
      } else if (difference.inHours < 1) {
        timeAgo = '${difference.inMinutes} phút trước';
      } else if (difference.inDays < 1) {
        timeAgo = '${difference.inHours} giờ trước';
      } else {
        timeAgo = '${difference.inDays} ngày trước';
      }

      return Row(
        children: [
          Icon(
            Icons.check_circle,
            size: 14,
            color: AppTheme.successGreen,
          ),
          const SizedBox(width: 4),
          Text(
            'Lần cuối: $timeAgo',
            style: const TextStyle(
              fontSize: 11,
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Icon(
            Icons.schedule,
            size: 14,
            color: AppTheme.textSecondary,
          ),
          const SizedBox(width: 4),
          const Text(
            'Chưa thực thi',
            style: TextStyle(
              fontSize: 11,
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      );
    }
  }
}
